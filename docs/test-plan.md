# Claude 配置管理器 - 综合测试计划

## 测试概览

### 项目状态
- **代码质量评分**: 96%
- **现有测试覆盖率**: 85%+
- **目标**: 确保完全替代原 `switch-claude.sh` 脚本功能

### 测试目标
1. 验证所有核心功能正常工作
2. 确保 Keychain 安全集成稳定可靠
3. 验证各种 macOS 版本兼容性
4. 确认性能和资源使用优化
5. 保证用户体验优于原脚本

## 测试策略

### 1. 测试金字塔结构

```
        E2E 测试 (5%)
      ─────────────────
     集成测试 (15%)
   ─────────────────────
  单元测试 (80%)
```

### 2. 测试类型分布

| 测试类型 | 覆盖范围 | 执行频率 | 自动化程度 |
|---------|---------|---------|-----------|
| 单元测试 | 80% | 每次构建 | 100% |
| 集成测试 | 15% | 每日构建 | 90% |
| 端到端测试 | 5% | 发布前 | 70% |
| 手动测试 | 关键路径 | 发布前 | 0% |

## 详细测试计划

### 阶段 1: 自动化测试 (已实现 85%)

#### 1.1 单元测试 ✅
**目标**: 验证各个组件独立功能
**状态**: 已完成
**覆盖范围**:
- ConfigService (776 行测试代码)
- KeychainService (456 行测试代码)
- ProcessService (287 行测试代码)

**测试重点**:
- [x] 配置文件加载和解析
- [x] Token 存储和检索
- [x] 进程状态检测和管理
- [x] 错误处理和恢复
- [x] 并发安全性
- [x] 内存管理

#### 1.2 集成测试 (需要补充)
**目标**: 验证组件间协作
**状态**: 部分完成
**测试用例**:

1. **配置切换集成测试**
   - 配置加载 → Token 迁移 → 进程重启
   - 验证完整切换流程
   - 测试切换失败回滚机制

2. **Keychain 集成测试**
   - 新配置 Token 自动存储
   - 现有配置 Token 迁移
   - Keychain 访问权限验证

3. **UI 集成测试**
   - 菜单栏状态更新
   - 错误消息显示
   - 用户交互响应

### 阶段 2: 端到端测试

#### 2.1 功能验证测试
**目标**: 模拟真实用户场景

**测试场景 1: 初次使用**
```
前提: 全新安装，无现有配置
步骤:
1. 启动应用
2. 验证菜单栏图标出现
3. 点击菜单，查看"无配置文件"提示
4. 创建第一个配置文件
5. 验证配置自动加载和显示

预期结果:
- 应用正常启动
- 菜单栏显示正确状态
- 新配置被正确识别和加载
```

**测试场景 2: 配置迁移**
```
前提: 存在现有 switch-claude.sh 配置
步骤:
1. 准备多个 *-settings.json 文件
2. 启动应用
3. 验证所有配置自动发现
4. 检查 Token 自动迁移到 Keychain
5. 测试配置切换功能

预期结果:
- 所有现有配置被发现
- Token 成功迁移且原文件中被移除
- 配置切换正常工作
```

**测试场景 3: 错误处理**
```
前提: 模拟各种错误条件
步骤:
1. 测试无效配置文件格式
2. 测试 Keychain 访问被拒绝
3. 测试配置目录权限不足
4. 测试 Claude CLI 不存在

预期结果:
- 显示用户友好的错误消息
- 提供具体的解决建议
- 应用不崩溃，可恢复
```

#### 2.2 性能基准测试

**测试用例 1: 大量配置处理**
```
目标: 验证处理大量配置文件的性能
数据: 50+ 个配置文件
指标:
- 启动时间 < 3 秒
- 配置切换时间 < 1 秒
- 内存使用 < 50MB
```

**测试用例 2: 并发操作**
```
目标: 验证多个快速操作的稳定性
场景: 快速连续切换配置
指标:
- 无竞态条件
- 无内存泄漏
- 状态一致性
```

### 阶段 3: 兼容性测试

#### 3.1 macOS 版本兼容性

| macOS 版本 | 支持状态 | 测试重点 |
|-----------|---------|---------|
| macOS 10.15 (Catalina) | ✅ 支持 | 最低版本验证 |
| macOS 11.0 (Big Sur) | ✅ 支持 | 新权限模型 |
| macOS 12.0 (Monterey) | ✅ 支持 | UI 兼容性 |
| macOS 13.0 (Ventura) | ✅ 支持 | 新安全特性 |
| macOS 14.0 (Sonoma) | ✅ 支持 | 最新特性 |
| macOS 15.0 (Sequoia) | ✅ 支持 | 前瞻性测试 |

#### 3.2 硬件架构兼容性

| 架构 | 支持状态 | 测试要求 |
|-----|---------|---------|
| Intel x86_64 | ✅ 支持 | 原生执行 |
| Apple Silicon (M1/M2/M3) | ✅ 支持 | 原生执行 |
| Rosetta 2 | ✅ 兼容 | 性能验证 |

### 阶段 4: 用户验收测试 (UAT)

#### 4.1 原脚本功能对比

**功能对比表**:

| 功能 | switch-claude.sh | ClaudeConfigManager | 改进程度 |
|-----|-----------------|-------------------|----------|
| 配置切换 | 命令行 | 图形界面 | ⭐⭐⭐⭐⭐ |
| 配置发现 | 手动指定 | 自动扫描 | ⭐⭐⭐⭐⭐ |
| Token 管理 | 明文存储 | Keychain 加密 | ⭐⭐⭐⭐⭐ |
| 状态显示 | 无 | 实时状态 | ⭐⭐⭐⭐⭐ |
| 错误处理 | 基础 | 详细指导 | ⭐⭐⭐⭐⭐ |
| 易用性 | 技术用户 | 所有用户 | ⭐⭐⭐⭐⭐ |

#### 4.2 用户体验验证

**测试场景 1: 非技术用户**
```
用户档案: 不熟悉命令行的设计师
任务: 在两个 Claude 配置间切换
成功标准:
- 无需查看文档即可完成任务
- 整个过程不超过 30 秒
- 用户感到舒适和自信
```

**测试场景 2: 高级用户**
```
用户档案: 熟悉原脚本的开发者
任务: 迁移现有工作流程
成功标准:
- 所有现有配置无缝迁移
- 工作效率提升
- 愿意推荐给其他人
```

#### 4.3 使用场景测试

1. **日常工作切换**
   - 个人项目 ↔ 工作项目
   - 不同 API 限制的配置

2. **团队协作场景**
   - 共享配置文件
   - 标准化团队设置

3. **高频使用场景**
   - 每天多次切换
   - 批量处理任务

### 阶段 5: 安全测试

#### 5.1 数据安全测试

**Token 保护验证**:
- [x] Token 存储在 Keychain 而非文件
- [x] 内存中 Token 及时清理
- [x] UI 显示 Token 脱敏处理
- [x] 进程间 Token 传递安全

**权限验证**:
- [x] 最小权限原则
- [x] 沙盒限制遵守
- [x] 用户授权流程

#### 5.2 输入验证测试

**配置文件验证**:
- [x] JSON 格式验证
- [x] 字段类型检查
- [x] 值范围验证
- [x] 特殊字符处理

**用户输入验证**:
- [x] 配置名称规范
- [x] URL 格式验证
- [x] 数值参数范围

## 测试执行计划

### 每日构建测试 (自动)
```bash
# 运行所有单元测试
xcodebuild test -scheme ClaudeConfigManager

# 代码覆盖率检查
xcodebuild test -scheme ClaudeConfigManager -enableCodeCoverage YES

# 静态代码分析
swiftlint
```

### 发布前测试 (手动 + 自动)

#### 预发布检查清单
- [ ] 所有单元测试通过 (100%)
- [ ] 集成测试通过 (90%+)
- [ ] 端到端测试通过 (70%+)
- [ ] 性能基准达标
- [ ] 多系统兼容性验证
- [ ] 安全扫描通过
- [ ] 用户验收测试完成

#### 发布标准
- **代码覆盖率**: ≥ 85%
- **性能指标**: 符合基准要求
- **兼容性**: 支持 macOS 10.15+
- **安全性**: 无已知安全漏洞
- **用户体验**: UAT 满意度 ≥ 90%

## 测试环境

### 测试设备配置

**最小配置环境**:
- macOS 10.15 + Intel Mac
- 8GB RAM
- Claude CLI 已安装

**标准配置环境**:
- macOS 13.0 + Apple Silicon
- 16GB RAM
- 多个 Claude 配置文件

**高端配置环境**:
- macOS 15.0 + M3 Pro
- 32GB RAM
- 大量配置文件 (50+)

### 测试数据

**配置文件样本**:
```
测试配置集合/
├── work-settings.json        # 工作配置
├── personal-settings.json    # 个人配置
├── team-settings.json        # 团队配置
├── invalid-format.json       # 无效格式测试
├── missing-token.json        # 缺少 Token 测试
└── large-config.json         # 大文件测试
```

## 测试报告

### 缺陷管理

**严重性分级**:
- **Critical**: 应用崩溃、数据丢失
- **Major**: 核心功能无法使用
- **Minor**: 非关键功能异常
- **Trivial**: UI 显示问题

**解决优先级**:
- Critical + Major: 阻止发布
- Minor: 发布前修复
- Trivial: 后续版本修复

### 测试度量

**质量指标**:
- 代码覆盖率: 85%+
- 缺陷密度: < 2 缺陷/KLOC
- 测试执行率: 100%
- 自动化率: 85%+

**性能指标**:
- 应用启动时间: < 3 秒
- 配置切换时间: < 1 秒
- 内存使用峰值: < 50MB
- CPU 使用率: < 5% (空闲时)

## 测试工具和框架

### 自动化测试工具
- **XCTest**: 单元测试和集成测试
- **Swift Testing**: 现代测试框架
- **XCUITest**: UI 自动化测试

### 性能测试工具
- **Instruments**: 内存和性能分析
- **XCTest Metrics**: 性能基准测试
- **Activity Monitor**: 系统资源监控

### 代码质量工具
- **SwiftLint**: 代码规范检查
- **Xcode Analyzer**: 静态代码分析
- **SonarQube**: 代码质量评估

## 风险评估

### 高风险区域
1. **Keychain 集成**: 权限和兼容性问题
2. **进程管理**: 系统级操作可能失败
3. **文件系统访问**: 权限和沙盒限制
4. **UI 响应性**: 长时间操作的用户体验

### 缓解策略
1. **充分测试**: 覆盖所有边界情况
2. **降级方案**: 提供备用实现
3. **用户指导**: 详细的错误处理和帮助
4. **监控机制**: 实时状态反馈

## 持续改进

### 测试反馈循环
1. **每日构建**: 快速发现回归问题
2. **用户反馈**: 收集实际使用问题
3. **性能监控**: 持续优化系统性能
4. **测试增强**: 基于问题补充测试用例

### 未来测试计划
1. **自动化覆盖扩展**: 提升到 90%+
2. **性能基准细化**: 更多场景的性能测试
3. **用户体验测试**: 定期 UX 评估
4. **兼容性前瞻**: 新系统版本适配

---

**测试负责人**: 开发团队
**文档版本**: 1.0
**最后更新**: 2025-07-27
**下次审查**: 发布前一周