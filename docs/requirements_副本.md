# Claude 配置管理器 - macOS 菜单栏应用需求规范

## 项目概述

将现有的 switch-claude.sh 脚本转换为用户友好的 macOS 菜单栏应用，提供图形化界面来管理 Claude CLI 配置。

## 功能需求

### 1. 核心功能需求

#### 1.1 配置管理
- **查看配置列表**: 显示所有可用的 Claude 配置（*-settings.json 文件）
- **切换配置**: 快速切换当前活动的 Claude 配置
- **添加新配置**: 创建新的配置文件
- **删除配置**: 移除不需要的配置文件
- **编辑配置**: 修改现有配置的参数

#### 1.2 状态显示
- **当前配置指示**: 清晰显示当前活动的配置
- **配置详情**: 显示配置的关键信息（Base URL、Token 预览）
- **连接状态**: 显示 Claude CLI 的连接状态

#### 1.3 Claude CLI 集成
- **启动 Claude**: 从菜单栏直接启动 Claude CLI
- **进程管理**: 检测和管理 Claude 进程
- **智能重启**: 配置切换时自动重启 Claude 进程

### 2. 用户界面需求

#### 2.1 菜单栏图标
- **状态指示**: 图标应反映当前连接状态
  - 绿色: Claude 正常运行
  - 橙色: 配置存在但 Claude 未运行
  - 红色: 配置错误或无配置
  - 灰色: 应用未激活

#### 2.2 下拉菜单结构
```
Claude 配置管理器
├── 当前配置: [配置名称]
├── ──────────────────
├── 🔄 切换配置
│   ├── ● inst (当前)
│   ├── ○ duck
│   ├── ○ yescode
│   └── ○ ...
├── ──────────────────
├── ⚙️ 管理配置
│   ├── ➕ 添加配置...
│   ├── ✏️ 编辑配置...
│   └── 🗑️ 删除配置...
├── ──────────────────
├── 🚀 启动 Claude
├── 🔄 重启 Claude
├── ⏹️ 停止 Claude
├── ──────────────────
├── ℹ️ 关于
├── ⚙️ 偏好设置...
└── 🚪 退出
```

#### 2.3 配置管理窗口
- **配置列表**: 表格形式显示所有配置
- **配置详情**: 显示选中配置的详细信息
- **编辑界面**: 表单形式编辑配置参数

#### 2.4 偏好设置窗口
- **启动设置**: 开机自启动选项
- **通知设置**: 配置切换成功/失败通知
- **更新设置**: 自动检查更新选项

### 3. 数据需求

#### 3.1 配置文件格式
- 保持与现有 *-settings.json 格式兼容
- 支持配置文件的备份和恢复
- 配置文件版本控制

#### 3.2 应用设置
- 用户偏好设置存储
- 最近使用的配置记录
- 窗口位置和大小记忆

### 4. 安全需求

#### 4.1 认证信息保护
- **敏感数据处理**: 使用 macOS Keychain 存储 API Token
- **显示保护**: Token 只显示前几位字符
- **传输安全**: 确保配置切换过程中数据安全

#### 4.2 文件权限
- **配置文件保护**: 适当的文件权限设置
- **访问控制**: 防止未授权访问配置文件

## 非功能需求

### 1. 性能需求
- **启动时间**: 应用启动时间 < 2 秒
- **响应时间**: 配置切换操作 < 1 秒
- **内存使用**: 应用内存占用 < 50MB
- **CPU 使用**: 空闲时 CPU 使用率接近 0%

### 2. 可用性需求
- **直观操作**: 所有操作应在 3 次点击内完成
- **错误恢复**: 提供清晰的错误信息和恢复建议
- **快捷键**: 支持常用操作的快捷键
- **国际化**: 支持中文和英文界面

### 3. 兼容性需求
- **系统版本**: 支持 macOS 10.15+ (Catalina 及以上)
- **架构支持**: 同时支持 Intel 和 Apple Silicon
- **Claude CLI**: 兼容现有 Claude CLI 版本

### 4. 可靠性需求
- **配置保护**: 配置切换失败时自动恢复
- **进程监控**: 监控 Claude 进程状态
- **日志记录**: 详细的操作日志用于问题诊断

### 5. 可维护性需求
- **模块化设计**: 清晰的代码结构和职责分离
- **单元测试**: 核心功能的单元测试覆盖率 > 80%
- **文档完整**: 完整的开发和用户文档

## 技术约束

### 1. 开发平台
- **开发语言**: Swift + SwiftUI
- **最低支持**: Xcode 12+, macOS 10.15+
- **包管理**: Swift Package Manager

### 2. 架构约束
- **设计模式**: MVVM 架构
- **数据存储**: UserDefaults + Keychain + JSON 文件
- **UI 框架**: SwiftUI + AppKit (菜单栏功能)

### 3. 分发约束
- **签名要求**: Apple Developer ID 签名
- **公证要求**: 通过 Apple 公证流程
- **分发方式**: 独立 .app 文件，支持 Homebrew 安装

### 4. 依赖约束
- **最小依赖**: 尽量使用系统自带框架
- **第三方库**: 仅在必要时使用，优先选择轻量级库

## 验收标准

### 1. 功能验收
- [ ] 能够识别和显示所有现有配置
- [ ] 配置切换功能正常工作
- [ ] 新配置创建和删除功能正常
- [ ] Claude CLI 启动和进程管理正常
- [ ] 菜单栏状态正确反映当前状态

### 2. 性能验收
- [ ] 应用启动时间满足要求
- [ ] 配置切换响应时间满足要求
- [ ] 内存和 CPU 使用满足要求

### 3. 用户体验验收
- [ ] 界面直观易用
- [ ] 错误信息清晰有用
- [ ] 操作流程顺畅

### 4. 兼容性验收
- [ ] 在支持的 macOS 版本上正常运行
- [ ] 与现有 Claude CLI 配置完全兼容
- [ ] 不破坏现有工作流程

## 项目约束

### 1. 时间约束
- **开发周期**: 预计 2-3 周完成 MVP 版本
- **迭代周期**: 每周一个迭代

### 2. 资源约束
- **开发人员**: 1 名 macOS 开发者
- **测试环境**: 不同 macOS 版本的测试设备

### 3. 质量约束
- **代码质量**: 遵循 Swift 编码规范
- **测试要求**: 核心功能必须有单元测试
- **文档要求**: 关键模块必须有注释和文档